import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Complete CMS setup function
export const initializeCMS = mutation({
  args: {},
  handler: async (ctx) => {
    // TODO: Add authentication check when needed
    // For now, allow setup without authentication for initial setup

    const results = {
      contentTypes: [] as any[],
      content: [] as any[],
      errors: [] as string[],
    };

    try {
      // First, initialize content types directly
      const now = Date.now();

      // Create essential content types
      const contentTypes = [
        {
          name: "hero_section",
          label: "Hero Section",
          description: "Main hero section with title, subtitle, and call-to-action",
          icon: "Layout",
          category: "sections",
          fields: [
            { name: "title", label: "Title", type: "text", required: true },
            { name: "subtitle", label: "Subtitle", type: "richText", required: false },
            { name: "backgroundImage", label: "Background Image", type: "image", required: false },
            { name: "ctaText", label: "CTA Button Text", type: "text", required: false },
            { name: "ctaUrl", label: "CTA Button URL", type: "url", required: false },
          ],
          settings: { allowMultiple: false, isSystem: true, sortable: false },
        },
        {
          name: "content_section",
          label: "Content Section",
          description: "General content section with title, content, and optional image",
          icon: "FileText",
          category: "sections",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: true },
            { name: "subtitle", label: "Subtitle", type: "text", required: false },
            { name: "content", label: "Content", type: "richText", required: true },
            { name: "image", label: "Section Image", type: "image", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "service_card",
          label: "Service Card",
          description: "Service or feature card with icon and description",
          icon: "Briefcase",
          category: "services",
          fields: [
            { name: "title", label: "Service Title", type: "text", required: true },
            { name: "description", label: "Description", type: "richText", required: true },
            { name: "icon", label: "Icon", type: "text", required: false },
            { name: "image", label: "Service Image", type: "image", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "testimonial",
          label: "Testimonial",
          description: "Customer testimonial with quote, author, and company",
          icon: "Quote",
          category: "testimonials",
          fields: [
            { name: "quote", label: "Quote", type: "richText", required: true },
            { name: "author", label: "Author Name", type: "text", required: true },
            { name: "position", label: "Position/Title", type: "text", required: false },
            { name: "company", label: "Company", type: "text", required: false },
            { name: "rating", label: "Rating (1-5)", type: "number", required: false, defaultValue: 5 },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "contact_info",
          label: "Contact Information",
          description: "Contact details including address, phone, email, and hours",
          icon: "Phone",
          category: "contact",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: false },
            { name: "address", label: "Address", type: "richText", required: false },
            { name: "phone", label: "Phone Number", type: "text", required: false },
            { name: "email", label: "Email Address", type: "email", required: false },
            { name: "hours", label: "Business Hours", type: "richText", required: false },
            { name: "socialLinks", label: "Social Media Links", type: "array", required: false },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
        {
          name: "cta_section",
          label: "Call to Action Section",
          description: "Call to action section with title, description, and buttons",
          icon: "ArrowRight",
          category: "sections",
          fields: [
            { name: "title", label: "CTA Title", type: "text", required: true },
            { name: "description", label: "Description", type: "richText", required: false },
            { name: "primaryButtonText", label: "Primary Button Text", type: "text", required: false },
            { name: "primaryButtonUrl", label: "Primary Button URL", type: "url", required: false },
            { name: "secondaryButtonText", label: "Secondary Button Text", type: "text", required: false },
            { name: "secondaryButtonUrl", label: "Secondary Button URL", type: "url", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
      ];

      // Create content types and store their IDs
      const contentTypeIds: Record<string, any> = {};
      for (const contentType of contentTypes) {
        try {
          // Check if content type already exists
          const existing = await ctx.db
            .query("contentTypes")
            .withIndex("by_name", (q) => q.eq("name", contentType.name))
            .first();

          if (!existing) {
            const id = await ctx.db.insert("contentTypes", {
              ...contentType,
              createdAt: now,
              updatedAt: now,
            });
            contentTypeIds[contentType.name] = id;
            results.contentTypes.push({ name: contentType.name, id, status: "created" });
          } else {
            contentTypeIds[contentType.name] = existing._id;
            results.contentTypes.push({ name: contentType.name, id: existing._id, status: "exists" });
          }
        } catch (error) {
          results.errors.push(`Failed to create content type ${contentType.name}: ${error}`);
        }
      }

      // Get content type IDs for creating content
      const heroContentType = { _id: contentTypeIds.hero_section };
      const contentSectionType = { _id: contentTypeIds.content_section };
      const serviceCardType = { _id: contentTypeIds.service_card };
      const testimonialType = { _id: contentTypeIds.testimonial };
      const contactInfoType = { _id: contentTypeIds.contact_info };
      const ctaSectionType = { _id: contentTypeIds.cta_section };



      // Create initial content items
      const initialContent = [
        // Home Hero Section
        {
          identifier: "home-hero",
          language: "en",
          contentTypeId: heroContentType?._id,
          status: "published" as const,
          data: {
            title: "Leading Technology Solutions in Equatorial Guinea",
            subtitle: "OfficeTech delivers cutting-edge cybersecurity, network infrastructure, and IT training solutions to protect and empower your business in the digital age.",
            ctaText: "Get Started Today",
            ctaUrl: "/contact",
          },
        },
        // Services Banner
        {
          identifier: "services-banner",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Our Services",
            subtitle: "Comprehensive Technology Solutions",
            content: "From cybersecurity to network infrastructure, we provide the complete technology stack your business needs to thrive in today's digital landscape.",
          },
        },
        // Trusted Partners Section
        {
          identifier: "trusted-partners",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Trusted by Leading Organizations",
            subtitle: "Our Partners",
            content: "We work with industry-leading partners to deliver world-class technology solutions to businesses across Equatorial Guinea.",
          },
        },
        // Who We Are Section
        {
          identifier: "who-we-are",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Who We Are",
            subtitle: "15+ Years of Excellence",
            content: "OfficeTech has been at the forefront of technology innovation in Equatorial Guinea for over 15 years. We specialize in cybersecurity, network solutions, electronic surveillance, and IT training, helping businesses secure their digital assets and optimize their operations.",
          },
        },
        // Key Features Section
        {
          identifier: "key-features",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Why Choose OfficeTech?",
            subtitle: "Our Competitive Advantages",
            content: "We combine local expertise with global standards to deliver technology solutions that are both cutting-edge and perfectly suited to the Equatorial Guinea market.",
          },
        },
        // CTA Section
        {
          identifier: "cta-section",
          language: "en",
          contentTypeId: ctaSectionType?._id,
          status: "published" as const,
          data: {
            title: "Ready to Secure Your Business?",
            description: "Get a free consultation and discover how our technology solutions can protect and grow your business.",
            primaryButtonText: "Get Free Consultation",
            primaryButtonUrl: "/contact",
            secondaryButtonText: "View Our Services",
            secondaryButtonUrl: "/services",
          },
        },
        // Contact Information
        {
          identifier: "contact-info",
          language: "en",
          contentTypeId: contactInfoType?._id,
          status: "published" as const,
          data: {
            title: "Get in Touch",
            address: "Malabo, Equatorial Guinea<br>Central Business District",
            phone: "+*********** 789",
            email: "<EMAIL>",
            hours: "Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 2:00 PM<br>24/7 Emergency Support Available",
            socialLinks: [
              { name: "LinkedIn", url: "https://linkedin.com/company/officetech-eg" },
              { name: "Facebook", url: "https://facebook.com/officetecheg" },
              { name: "Twitter", url: "https://twitter.com/officetecheg" },
            ],
          },
        },
        // About Page Hero
        {
          identifier: "about-hero",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "About OfficeTech",
            subtitle: "Leading Technology Solutions in Equatorial Guinea",
            content: "For over 15 years, we've been at the forefront of technology innovation in Equatorial Guinea, helping businesses secure their digital assets and optimize their operations with cutting-edge solutions. Our team of certified professionals delivers world-class cybersecurity, network infrastructure, and IT training services.",
          },
        },
        // About Values Section
        {
          identifier: "about-values",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Our Values",
            subtitle: "The principles that guide everything we do",
            content: "At OfficeTech, our values drive every decision we make and every solution we deliver. We believe in excellence, partnership, reliability, and innovation as the foundation of our success and our clients' success.",
          },
        },
        // Services Page Hero
        {
          identifier: "services-hero",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Our Services",
            subtitle: "Comprehensive Technology Solutions",
            content: "From cybersecurity to network infrastructure, we provide the complete technology stack your business needs to thrive in today's digital landscape. Our expert team delivers world-class solutions tailored to the unique needs of businesses in Equatorial Guinea.",
          },
        },
        // Contact Page Hero
        {
          identifier: "contact-hero",
          language: "en",
          contentTypeId: contentSectionType?._id,
          status: "published" as const,
          data: {
            title: "Contact Us",
            subtitle: "Get in Touch with Our Experts",
            content: "Ready to secure and optimize your business technology? Our team of certified professionals is here to help you find the perfect solution for your needs. Contact us today for a free consultation.",
          },
        },
      ];

      // Create testimonials
      const testimonials = [
        {
          identifier: "testimonial-1",
          language: "en",
          contentTypeId: testimonialType?._id,
          status: "published" as const,
          data: {
            quote: "OfficeTech transformed our IT infrastructure completely. Their cybersecurity solutions have given us peace of mind and their support is exceptional.",
            author: "Maria Santos",
            position: "IT Director",
            company: "Banco Nacional de Guinea Ecuatorial",
            rating: 5,
          },
        },
        {
          identifier: "testimonial-2",
          language: "en",
          contentTypeId: testimonialType?._id,
          status: "published" as const,
          data: {
            quote: "The network solutions provided by OfficeTech have significantly improved our operational efficiency. Highly recommended for any business serious about technology.",
            author: "Carlos Rodriguez",
            position: "Operations Manager",
            company: "Equatorial Guinea Telecom",
            rating: 5,
          },
        },
        {
          identifier: "testimonial-3",
          language: "en",
          contentTypeId: testimonialType?._id,
          status: "published" as const,
          data: {
            quote: "Professional, reliable, and knowledgeable. OfficeTech's surveillance systems have enhanced our security posture tremendously.",
            author: "Ana Obiang",
            position: "Security Director",
            company: "Port Authority of Malabo",
            rating: 5,
          },
        },
      ];

      // Create service cards based on content.md
      const serviceCards = [
        {
          identifier: "service-cybersecurity",
          language: "en",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "Cybersecurity",
            description: "Protecting Your Digital World at Every Stage. Comprehensive cybersecurity solutions including prevention, detection, response, recovery, and continuous improvement to safeguard your business against cyber threats.",
            icon: "Shield",
          },
        },
        {
          identifier: "service-network",
          language: "en",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "Network Solutions",
            description: "International and national connectivity solutions including MPLS, SD-WAN, and VPL. High-speed internet, private networks, and security solutions with 24/7 technical support.",
            icon: "Wifi",
          },
        },
        {
          identifier: "service-surveillance",
          language: "en",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "Electronic Security",
            description: "Comprehensive electronic security solutions including IP cameras with night vision and 4K resolution, alarm systems, access control, and professional installation with 24/7 monitoring.",
            icon: "Eye",
          },
        },
        {
          identifier: "service-training",
          language: "en",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "IT Training & Support",
            description: "Professional IT training programs, technical support, and system maintenance to empower your team with the latest technology skills and ensure optimal system performance.",
            icon: "GraduationCap",
          },
        },
      ];

      // Create Spanish content based on content.md
      const spanishContent = [
        // Spanish Home Hero
        {
          identifier: "home-hero",
          language: "es",
          contentTypeId: heroContentType?._id,
          status: "published" as const,
          data: {
            title: "Soluciones Tecnológicas Líderes en Guinea Ecuatorial",
            subtitle: "OfficeTech ofrece soluciones avanzadas de ciberseguridad, infraestructura de red y capacitación en TI para proteger y potenciar su negocio en la era digital.",
            ctaText: "Comience Hoy",
            ctaUrl: "/contact",
          },
        },
        // Spanish Services
        {
          identifier: "service-cybersecurity",
          language: "es",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "Ciberseguridad",
            description: "Protegiendo tu Mundo Digital en Cada Fase. Soluciones integrales de ciberseguridad incluyendo prevención, detección, respuesta, recuperación y mejora continua para proteger su negocio contra amenazas cibernéticas.",
            icon: "Shield",
          },
        },
        {
          identifier: "service-surveillance",
          language: "es",
          contentTypeId: serviceCardType?._id,
          status: "published" as const,
          data: {
            title: "Seguridad Electrónica",
            description: "Soluciones integrales de seguridad electrónica incluyendo cámaras IP con visión nocturna y resolución 4K, sistemas de alarmas, control de acceso e instalación profesional con monitoreo 24/7.",
            icon: "Eye",
          },
        },
        // Spanish Contact Info
        {
          identifier: "contact-info",
          language: "es",
          contentTypeId: contactInfoType?._id,
          status: "published" as const,
          data: {
            title: "Contáctanos",
            address: "Malabo, Guinea Ecuatorial<br>Distrito Central de Negocios",
            phone: "+*********** 789",
            email: "<EMAIL>",
            hours: "Lunes - Viernes: 8:00 AM - 6:00 PM<br>Sábado: 9:00 AM - 2:00 PM<br>Soporte de Emergencia 24/7 Disponible",
            socialLinks: [
              { name: "LinkedIn", url: "https://linkedin.com/company/officetech-eg" },
              { name: "Facebook", url: "https://facebook.com/officetecheg" },
              { name: "Twitter", url: "https://twitter.com/officetecheg" },
            ],
          },
        },
      ];

      // Combine all content
      const allContent = [...initialContent, ...testimonials, ...serviceCards, ...spanishContent];

      // Insert content items
      for (const contentItem of allContent) {
        if (contentItem.contentTypeId) {
          try {
            const contentId = await ctx.db.insert("content", {
              identifier: contentItem.identifier,
              language: contentItem.language,
              data: contentItem.data,
              contentTypeId: contentItem.contentTypeId,
              status: contentItem.status,
              version: 1,
              createdBy: "system",
              createdAt: now,
              updatedAt: now,
            });
            results.content.push({ identifier: contentItem.identifier, id: contentId, status: "created" });
          } catch (error) {
            results.errors.push(`Failed to create content ${contentItem.identifier}: ${error}`);
          }
        } else {
          results.errors.push(`Content type not found for ${contentItem.identifier}`);
        }
      }

      return results;
    } catch (error) {
      results.errors.push(`Setup failed: ${error}`);
      return results;
    }
  },
});

// Function to check if CMS is already initialized
export const checkCMSStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const contentTypes = await ctx.db.query("contentTypes").collect();
    const content = await ctx.db.query("content").collect();
    
    return {
      contentTypesCount: contentTypes.length,
      contentItemsCount: content.length,
      isInitialized: contentTypes.length > 0 && content.length > 0,
      contentTypes: contentTypes.map(ct => ({ name: ct.name, label: ct.label })),
      contentItems: content.map(c => ({ identifier: c.identifier, language: c.language, status: c.status })),
    };
  },
});

// Function to update hero content with new fields
export const updateHeroContentFields = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];

      // Get existing hero content
      const englishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "en")
        )
        .first();

      const spanishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "es")
        )
        .first();

      if (!englishHero || !spanishHero) {
        return {
          success: false,
          message: "Hero content not found",
        };
      }

      // Update English hero content
      const updatedEnglishData = {
        ...englishHero.data,
        secondaryCtaText: "View Our Work",
        secondaryCtaUrl: "#work",
        badge1Text: "24/7 Support",
        badge2Text: "Secure Solutions",
        experienceYears: "15+",
        experienceLabel: "Years\nExperience",
        heroImageAlt: "Professional IT team working on technology solutions"
      };

      // Update Spanish hero content
      const updatedSpanishData = {
        ...spanishHero.data,
        secondaryCtaText: "Ver Nuestro Trabajo",
        secondaryCtaUrl: "#trabajo",
        badge1Text: "Soporte 24/7",
        badge2Text: "Soluciones Seguras",
        experienceYears: "15+",
        experienceLabel: "Años de\nExperiencia",
        heroImageAlt: "Equipo profesional de TI trabajando en soluciones tecnológicas"
      };

      const now = Date.now();

      // Update English content
      await ctx.db.patch(englishHero._id, {
        data: updatedEnglishData,
        version: englishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "en", status: "updated" });

      // Update Spanish content
      await ctx.db.patch(spanishHero._id, {
        data: updatedSpanishData,
        version: spanishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "es", status: "updated" });

      return {
        success: true,
        message: "Hero content updated successfully",
        results,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to update hero content: ${error}`,
      };
    }
  },
});

// Function to fix hero button text
export const fixHeroButtonText = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];

      // Get existing hero content
      const englishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "en")
        )
        .first();

      const spanishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "es")
        )
        .first();

      if (!englishHero || !spanishHero) {
        return {
          success: false,
          message: "Hero content not found",
        };
      }

      // Update English hero content with proper button text
      const updatedEnglishData = {
        ...englishHero.data,
        secondaryCtaText: "View Services",
        secondaryCtaUrl: "/services",
      };

      // Update Spanish hero content with proper button text
      const updatedSpanishData = {
        ...spanishHero.data,
        secondaryCtaText: "Ver Servicios",
        secondaryCtaUrl: "/services",
      };

      const now = Date.now();

      // Update English content
      await ctx.db.patch(englishHero._id, {
        data: updatedEnglishData,
        version: englishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "en", status: "updated" });

      // Update Spanish content
      await ctx.db.patch(spanishHero._id, {
        data: updatedSpanishData,
        version: spanishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "es", status: "updated" });

      return {
        success: true,
        message: "Hero button text updated successfully",
        results,
        buttonText: { en: "View Services", es: "Ver Servicios" }
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to update hero button text: ${error}`,
      };
    }
  },
});

// Function to fix button URLs
export const fixButtonUrls = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];

      // Get existing hero content
      const englishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "en")
        )
        .first();

      const spanishHero = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", "home-hero").eq("language", "es")
        )
        .first();

      if (!englishHero || !spanishHero) {
        return {
          success: false,
          message: "Hero content not found",
        };
      }

      // Update English hero content with proper URLs
      const updatedEnglishData = {
        ...englishHero.data,
        ctaUrl: "/contact", // Keep primary CTA pointing to contact
        secondaryCtaUrl: "/services", // Change secondary CTA to services page
      };

      // Update Spanish hero content with proper URLs
      const updatedSpanishData = {
        ...spanishHero.data,
        ctaUrl: "/contact", // Keep primary CTA pointing to contact
        secondaryCtaUrl: "/services", // Change secondary CTA to services page
      };

      const now = Date.now();

      // Update English content
      await ctx.db.patch(englishHero._id, {
        data: updatedEnglishData,
        version: englishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "en", status: "updated" });

      // Update Spanish content
      await ctx.db.patch(spanishHero._id, {
        data: updatedSpanishData,
        version: spanishHero.version + 1,
        updatedAt: now,
      });
      results.push({ identifier: "home-hero", language: "es", status: "updated" });

      return {
        success: true,
        message: "Button URLs fixed successfully",
        results,
        urls: {
          primaryCta: "/contact",
          secondaryCta: "/services"
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to fix button URLs: ${error}`,
      };
    }
  },
});

// Function to create missing content types and content for home page sections
export const createMissingHomePageContent = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = {
        contentTypes: [] as any[],
        content: [] as any[],
        errors: [] as string[],
      };

      const now = Date.now();

      // Create missing content types
      const newContentTypes = [
        {
          name: "about_section",
          label: "About Section",
          description: "About us section with stats and description",
          icon: "Users",
          category: "sections",
          fields: [
            { name: "title", label: "Title", type: "text", required: true, placeholder: "Enter section title" },
            { name: "description", label: "Description", type: "richText", required: true, placeholder: "Enter description" },
            { name: "yearsExperience", label: "Years Experience", type: "text", required: false, defaultValue: "15+" },
            { name: "clientsServed", label: "Clients Served", type: "text", required: false, defaultValue: "500+" },
            { name: "satisfaction", label: "Satisfaction Rate", type: "text", required: false, defaultValue: "99%" },
            { name: "image", label: "Section Image", type: "image", required: false },
            { name: "imageAlt", label: "Image Alt Text", type: "text", required: false },
            { name: "badgeTitle", label: "Badge Title", type: "text", required: false, defaultValue: "Certified Experts" },
            { name: "badgeSubtitle", label: "Badge Subtitle", type: "text", required: false, defaultValue: "Industry Leaders" },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
        {
          name: "services_banner",
          label: "Services Banner",
          description: "Scrolling banner with service names",
          icon: "Briefcase",
          category: "sections",
          fields: [
            { name: "services", label: "Services List", type: "array", required: true, placeholder: "Enter services separated by commas" },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
        {
          name: "key_features",
          label: "Key Features",
          description: "Key features section with feature cards",
          icon: "Star",
          category: "sections",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: true, placeholder: "Enter section title" },
            { name: "subtitle", label: "Section Subtitle", type: "text", required: false, placeholder: "Enter subtitle" },
            { name: "features", label: "Features", type: "array", required: true, placeholder: "Features data" },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
        {
          name: "trusted_partners",
          label: "Trusted Partners",
          description: "Partners/logos section",
          icon: "Handshake",
          category: "sections",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: true, placeholder: "Enter section title" },
            { name: "description", label: "Description", type: "text", required: false, placeholder: "Enter description" },
            { name: "partners", label: "Partners", type: "array", required: true, placeholder: "Partners data" },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
      ];

      // Create content types and store their IDs
      const contentTypeIds: Record<string, any> = {};
      for (const contentType of newContentTypes) {
        try {
          // Check if content type already exists
          const existing = await ctx.db
            .query("contentTypes")
            .withIndex("by_name", (q) => q.eq("name", contentType.name))
            .first();

          if (!existing) {
            const id = await ctx.db.insert("contentTypes", {
              ...contentType,
              createdAt: now,
              updatedAt: now,
            });
            contentTypeIds[contentType.name] = id;
            results.contentTypes.push({ name: contentType.name, id, status: "created" });
          } else {
            contentTypeIds[contentType.name] = existing._id;
            results.contentTypes.push({ name: contentType.name, id: existing._id, status: "exists" });
          }
        } catch (error) {
          results.errors.push(`Failed to create content type ${contentType.name}: ${error}`);
        }
      }

      // Create initial content items
      const initialContent = [
        // Who We Are Section
        {
          identifier: "who-we-are",
          language: "en",
          contentTypeId: contentTypeIds.about_section,
          status: "published" as const,
          data: {
            title: "Who We Are",
            description: "Leading technology and security solutions provider in Equatorial Guinea, delivering innovative IT services that empower businesses to thrive in the digital age.",
            yearsExperience: "15+",
            clientsServed: "500+",
            satisfaction: "99%",
            badgeTitle: "Certified Experts",
            badgeSubtitle: "Industry Leaders",
            imageAlt: "Professional IT team working",
          },
        },
        {
          identifier: "who-we-are",
          language: "es",
          contentTypeId: contentTypeIds.about_section,
          status: "published" as const,
          data: {
            title: "Quiénes Somos",
            description: "Proveedor líder de soluciones tecnológicas y de seguridad en Guinea Ecuatorial, ofreciendo servicios innovadores de TI que empoderan a las empresas para prosperar en la era digital.",
            yearsExperience: "15+",
            clientsServed: "500+",
            satisfaction: "99%",
            badgeTitle: "Expertos Certificados",
            badgeSubtitle: "Líderes de la Industria",
            imageAlt: "Equipo profesional de TI trabajando",
          },
        },
        // Trusted Partners Section
        {
          identifier: "trusted-partners",
          language: "en",
          contentTypeId: contentTypeIds.trusted_partners,
          status: "published" as const,
          data: {
            title: "Trusted Partners",
            description: "We work with industry-leading technology partners to deliver the best solutions for your business.",
            partners: [
              { name: "Microsoft", logo: "/partners/part1.jpg" },
              { name: "Cisco", logo: "/partners/part2.jpg" },
              { name: "VMware", logo: "/partners/part3.jpg" },
              { name: "Dell", logo: "/partners/part4.jpg" },
              { name: "Adobe", logo: "/partners/part5.jpg" },
              { name: "Google", logo: "/partners/part6.jpg" },
            ],
          },
        },
        {
          identifier: "trusted-partners",
          language: "es",
          contentTypeId: contentTypeIds.trusted_partners,
          status: "published" as const,
          data: {
            title: "Socios de Confianza",
            description: "Trabajamos con socios tecnológicos líderes en la industria para ofrecer las mejores soluciones para su negocio.",
            partners: [
              { name: "Microsoft", logo: "/partners/part1.jpg" },
              { name: "Cisco", logo: "/partners/part2.jpg" },
              { name: "VMware", logo: "/partners/part3.jpg" },
              { name: "Dell", logo: "/partners/part4.jpg" },
              { name: "Adobe", logo: "/partners/part5.jpg" },
              { name: "Google", logo: "/partners/part6.jpg" },
            ],
          },
        },
        // Services Banner Section
        {
          identifier: "services-banner",
          language: "en",
          contentTypeId: contentTypeIds.services_banner,
          status: "published" as const,
          data: {
            services: [
              "Cybersecurity Solutions",
              "Network Infrastructure",
              "Cloud Services",
              "IT Training",
              "Technical Support",
              "System Integration",
              "Web Development",
              "Digital Transformation"
            ],
          },
        },
        {
          identifier: "services-banner",
          language: "es",
          contentTypeId: contentTypeIds.services_banner,
          status: "published" as const,
          data: {
            services: [
              "Soluciones de Ciberseguridad",
              "Infraestructura de Red",
              "Servicios en la Nube",
              "Capacitación en TI",
              "Soporte Técnico",
              "Integración de Sistemas",
              "Desarrollo Web",
              "Transformación Digital"
            ],
          },
        },
        // Key Features Section
        {
          identifier: "key-features",
          language: "en",
          contentTypeId: contentTypeIds.key_features,
          status: "published" as const,
          data: {
            title: "Why Choose OfficeTech",
            subtitle: "Excellence in every aspect of our service",
            features: [
              {
                icon: "Clock",
                title: "24/7 Support",
                description: "Round-the-clock monitoring and support for all your systems",
              },
              {
                icon: "Users",
                title: "Expert Team",
                description: "Certified professionals with years of industry experience",
              },
              {
                icon: "Award",
                title: "Quality Assured",
                description: "ISO certified processes and industry-leading standards",
              },
            ],
          },
        },
        {
          identifier: "key-features",
          language: "es",
          contentTypeId: contentTypeIds.key_features,
          status: "published" as const,
          data: {
            title: "Por Qué Elegir OfficeTech",
            subtitle: "Excelencia en cada aspecto de nuestro servicio",
            features: [
              {
                icon: "Clock",
                title: "Soporte 24/7",
                description: "Monitoreo y soporte las 24 horas para todos sus sistemas",
              },
              {
                icon: "Users",
                title: "Equipo Experto",
                description: "Profesionales certificados con años de experiencia en la industria",
              },
              {
                icon: "Award",
                title: "Calidad Asegurada",
                description: "Procesos certificados ISO y estándares líderes en la industria",
              },
            ],
          },
        },
      ];

      // Insert content items
      for (const contentItem of initialContent) {
        if (contentItem.contentTypeId) {
          try {
            // Check if content already exists
            const existing = await ctx.db
              .query("content")
              .withIndex("by_identifier_language", (q) =>
                q.eq("identifier", contentItem.identifier).eq("language", contentItem.language)
              )
              .first();

            if (!existing) {
              const contentId = await ctx.db.insert("content", {
                identifier: contentItem.identifier,
                language: contentItem.language,
                data: contentItem.data,
                contentTypeId: contentItem.contentTypeId,
                status: contentItem.status,
                version: 1,
                createdBy: "system",
                createdAt: now,
                updatedAt: now,
              });
              results.content.push({ identifier: contentItem.identifier, id: contentId, status: "created" });
            } else {
              results.content.push({ identifier: contentItem.identifier, id: existing._id, status: "exists" });
            }
          } catch (error) {
            results.errors.push(`Failed to create content ${contentItem.identifier}: ${error}`);
          }
        } else {
          results.errors.push(`Content type not found for ${contentItem.identifier}`);
        }
      }

      return {
        success: true,
        message: "Missing home page content created successfully",
        results,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to create missing content: ${error}`,
      };
    }
  },
});

// Function to update partner logos with local images
export const updatePartnerLogos = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];

      // New partner data with local images
      const newPartners = [
        { name: "Microsoft", logo: "/partners/part1.jpg" },
        { name: "Cisco", logo: "/partners/part2.jpg" },
        { name: "VMware", logo: "/partners/part3.jpg" },
        { name: "Dell", logo: "/partners/part4.jpg" },
        { name: "Adobe", logo: "/partners/part5.jpg" },
        { name: "Google", logo: "/partners/part6.jpg" },
      ];

      // Update English version
      const englishContent = await ctx.db
        .query("content")
        .filter((q) => q.and(
          q.eq(q.field("identifier"), "trusted-partners"),
          q.eq(q.field("language"), "en")
        ))
        .first();

      if (englishContent) {
        await ctx.db.patch(englishContent._id, {
          data: {
            ...englishContent.data,
            partners: newPartners
          },
          updatedAt: Date.now()
        });
        results.push("Updated English trusted partners");
      }

      // Update Spanish version
      const spanishContent = await ctx.db
        .query("content")
        .filter((q) => q.and(
          q.eq(q.field("identifier"), "trusted-partners"),
          q.eq(q.field("language"), "es")
        ))
        .first();

      if (spanishContent) {
        await ctx.db.patch(spanishContent._id, {
          data: {
            ...spanishContent.data,
            partners: newPartners
          },
          updatedAt: Date.now()
        });
        results.push("Updated Spanish trusted partners");
      }

      return {
        success: true,
        results,
        partnersUpdated: newPartners.length
      };
    } catch (error) {
      console.error("Error updating partner logos:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  },
});

// Function to update all images with local images
export const updateAllImages = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];
      let updatedCount = 0;

      // Image mappings for different content types
      const imageMap = {
        "home-hero": "/img/img_presentation.jpg",
        "about-hero": "/img/Immg1.jpg",
        "services-hero": "/img/ai-generated-8684869_1920.jpg",
        "contact-hero": "/img/img_typing_white.jpg",
        "service-cybersecurity": "/img/cyber-security-1186529_1920.png",
        "service-network": "/img/img_cables1.jpg",
        "service-surveillance": "/img/img_field_tech.jpg",
        "service-training": "/img/img_presentation.jpg",
        "service-webdev": "/img/Coding_img.jpg",
        "who-we-are": "/img/Immg1.jpg",
      };

      // Update hero sections with heroImage field
      for (const [identifier, imagePath] of Object.entries(imageMap)) {
        // Update English version
        const englishContent = await ctx.db
          .query("content")
          .filter((q) => q.and(
            q.eq(q.field("identifier"), identifier),
            q.eq(q.field("language"), "en")
          ))
          .first();

        if (englishContent) {
          await ctx.db.patch(englishContent._id, {
            data: {
              ...englishContent.data,
              heroImage: { url: imagePath },
              image: { url: imagePath }
            },
            updatedAt: Date.now()
          });
          results.push(`Updated English ${identifier}`);
          updatedCount++;
        }

        // Update Spanish version
        const spanishContent = await ctx.db
          .query("content")
          .filter((q) => q.and(
            q.eq(q.field("identifier"), identifier),
            q.eq(q.field("language"), "es")
          ))
          .first();

        if (spanishContent) {
          await ctx.db.patch(spanishContent._id, {
            data: {
              ...spanishContent.data,
              heroImage: { url: imagePath },
              image: { url: imagePath }
            },
            updatedAt: Date.now()
          });
          results.push(`Updated Spanish ${identifier}`);
          updatedCount++;
        }
      }

      return {
        success: true,
        results,
        updatedCount
      };
    } catch (error) {
      console.error("Error updating images:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  },
});

// Function to add Spanish content specifically
export const addSpanishContent = mutation({
  args: {},
  handler: async (ctx) => {
    // Get existing content types
    const contentTypes = await ctx.db.query("contentTypes").collect();
    const contentTypeMap: Record<string, any> = {};
    contentTypes.forEach(ct => {
      contentTypeMap[ct.name] = ct._id;
    });

    // Check if Spanish content already exists
    const existingSpanishContent = await ctx.db
      .query("content")
      .withIndex("by_language", (q) => q.eq("language", "es"))
      .collect();

    if (existingSpanishContent.length > 0) {
      return {
        success: false,
        message: "Spanish content already exists",
        existingCount: existingSpanishContent.length,
      };
    }

    // Spanish content to add
    const spanishContent = [
      {
        identifier: "home-hero",
        language: "es",
        contentTypeId: contentTypeMap["hero_section"],
        status: "published" as const,
        data: {
          title: "Soluciones Tecnológicas Líderes en Guinea Ecuatorial",
          subtitle: "OfficeTech ofrece soluciones avanzadas de ciberseguridad, infraestructura de red y capacitación en TI para proteger y potenciar su negocio en la era digital.",
          ctaText: "Comience Hoy",
          ctaUrl: "/contact",
        },
      },
      {
        identifier: "services-banner",
        language: "es",
        contentTypeId: contentTypeMap["content_section"],
        status: "published" as const,
        data: {
          title: "Nuestros Servicios",
          subtitle: "Soluciones tecnológicas integrales para su negocio",
          content: "Ciberseguridad • Diseño Web • Vigilancia Electrónica • Capacitación en TI • Infraestructura de Red • Integración de Sistemas",
        },
      },
      {
        identifier: "contact-info",
        language: "es",
        contentTypeId: contentTypeMap["contact_info"],
        status: "published" as const,
        data: {
          title: "Contáctanos",
          address: "Malabo, Guinea Ecuatorial<br>Distrito Central de Negocios",
          phone: "+*********** 789",
          email: "<EMAIL>",
          hours: "Lunes - Viernes: 8:00 AM - 6:00 PM<br>Sábado: 9:00 AM - 2:00 PM<br>Soporte de Emergencia 24/7 Disponible",
        },
      },
      {
        identifier: "service-cybersecurity",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published" as const,
        data: {
          title: "Ciberseguridad",
          description: "Protegiendo tu Mundo Digital en Cada Fase. Soluciones integrales de ciberseguridad incluyendo prevención, detección, respuesta, recuperación y mejora continua para proteger su negocio contra amenazas cibernéticas.",
          icon: "Shield",
        },
      },
      {
        identifier: "service-surveillance",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published" as const,
        data: {
          title: "Seguridad Electrónica",
          description: "Soluciones integrales de seguridad electrónica incluyendo cámaras IP con visión nocturna y resolución 4K, sistemas de alarmas, control de acceso e instalación profesional con monitoreo 24/7.",
          icon: "Eye",
        },
      },
      {
        identifier: "service-network",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published" as const,
        data: {
          title: "Infraestructura de Red",
          description: "Diseño e implementación de redes empresariales robustas con conectividad confiable y escalabilidad futura para soportar el crecimiento de su negocio.",
          icon: "Network",
        },
      },
      {
        identifier: "service-training",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published" as const,
        data: {
          title: "Capacitación en TI",
          description: "Programas de capacitación especializados para desarrollar habilidades técnicas y mejorar la productividad del equipo con las últimas tecnologías.",
          icon: "GraduationCap",
        },
      },
      {
        identifier: "cta-section",
        language: "es",
        contentTypeId: contentTypeMap["cta_section"],
        status: "published" as const,
        data: {
          title: "¿Listo para Transformar su Negocio?",
          subtitle: "Asegurando su futuro digital con soluciones tecnológicas innovadoras y experiencia sin compromisos.",
          ctaText: "Contáctanos Hoy",
          ctaUrl: "/contact",
        },
      },
    ];

    // Add each content item
    const results = [];
    for (const content of spanishContent) {
      if (content.contentTypeId) {
        const contentId = await ctx.db.insert("content", {
          identifier: content.identifier,
          language: content.language,
          contentTypeId: content.contentTypeId,
          data: content.data,
          status: content.status,
          version: 1,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: "system",
        });
        results.push({ identifier: content.identifier, id: contentId });
      }
    }

    return {
      success: true,
      message: `Successfully added ${results.length} Spanish content items`,
      addedContent: results,
    };
  },
});

// Function to fix English "Who We Are" content to match Spanish version
export const fixEnglishWhoWeAre = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const results = [];

      // Get the existing English content
      const englishContent = await ctx.db
        .query("content")
        .filter((q) => q.and(
          q.eq(q.field("identifier"), "who-we-are"),
          q.eq(q.field("language"), "en")
        ))
        .first();

      if (englishContent) {
        await ctx.db.patch(englishContent._id, {
          data: {
            title: "Who We Are",
            subtitle: "15+ Years of Excellence",
            description: "OfficeTech has been at the forefront of technology innovation in Equatorial Guinea for over 15 years. We specialize in cybersecurity, network solutions, electronic surveillance, and IT training, helping businesses secure their digital assets and optimize their operations.",
            image: {
              url: "/img/Immg1.jpg",
              alt: "Professional IT team working on technology solutions"
            },
            stats: [
              {
                number: "15+",
                label: "Years Experience",
                description: "Serving Equatorial Guinea"
              },
              {
                number: "500+",
                label: "Satisfied Clients",
                description: "Across multiple sectors"
              },
              {
                number: "99%",
                label: "Client Satisfaction",
                description: "Quality guaranteed"
              }
            ]
          },
          updatedAt: Date.now()
        });
        results.push("Updated English who-we-are content");
      }

      return {
        success: true,
        results,
        message: "English Who We Are content updated successfully"
      };
    } catch (error) {
      console.error("Error fixing English Who We Are content:", error);
      throw error;
    }
  },
});
