/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as adminRoles from "../adminRoles.js";
import type * as analytics from "../analytics.js";
import type * as autoSetup from "../autoSetup.js";
import type * as contactForms from "../contactForms.js";
import type * as contacts from "../contacts.js";
import type * as content from "../content.js";
import type * as contentTypes from "../contentTypes.js";
import type * as contentVersioning from "../contentVersioning.js";
import type * as media from "../media.js";
import type * as reorganizeServices from "../reorganizeServices.js";
import type * as setup from "../setup.js";
import type * as siteSettings from "../siteSettings.js";
import type * as updateServices from "../updateServices.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  adminRoles: typeof adminRoles;
  analytics: typeof analytics;
  autoSetup: typeof autoSetup;
  contactForms: typeof contactForms;
  contacts: typeof contacts;
  content: typeof content;
  contentTypes: typeof contentTypes;
  contentVersioning: typeof contentVersioning;
  media: typeof media;
  reorganizeServices: typeof reorganizeServices;
  setup: typeof setup;
  siteSettings: typeof siteSettings;
  updateServices: typeof updateServices;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
