import { Link } from "react-router-dom";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { DynamicContent } from '@/components/content/DynamicContent';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { NetworkSolutionsContact } from "@/components/forms/NetworkSolutionsContact";
import {
  Network,
  Building2,
  Globe,
  Shield,
  Zap,
  Users,
  ArrowRight,
  CheckCircle,
  Phone,
  Mail,
  MapPin,
  Clock,
  Star,
  TrendingUp,
  Wifi,
  Home
} from "lucide-react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage, useTranslation } from '@/contexts/I18nContext';

const NetworkSolutionsContent = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all network solution cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for network solution cards and sort by order
  const dynamicNetworkSolutions = allContent?.filter(item =>
    item.contentType?.name === "network_solution" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];
  // Fallback network solutions data with bilingual support
  const fallbackNetworkSolutions = [
    {
      id: 'national-connectivity',
      title: language === 'en' ? 'National Connectivity' : 'Conectividad Nacional',
      description: language === 'en' ? 'Comprehensive network solutions across Equatorial Guinea (Bata, Malabo & Mongomo) with 24/7 support.' : 'Soluciones de red integrales en Guinea Ecuatorial (Bata, Malabo y Mongomo) con soporte 24/7.',
      icon: Network,
      features: language === 'en' ? ['Private Networks', 'High-Speed Internet', 'Security Solutions', '24/7 Support'] : ['Redes Privadas', 'Internet de Alta Velocidad', 'Soluciones de Seguridad', 'Soporte 24/7'],
      slug: 'national-connectivity'
    },
    {
      id: 'branch-office',
      title: language === 'en' ? 'Branch Office Connectivity' : 'Conectividad de Sucursales',
      description: language === 'en' ? 'Enterprise solutions to keep your branches connected, collaborative, and competitive worldwide.' : 'Soluciones empresariales para mantener tus sucursales conectadas, colaborativas y competitivas mundialmente.',
      icon: Building2,
      features: language === 'en' ? ['High-Speed Connectivity', 'Centralized Management', 'Enhanced Security', 'Cost Efficiency'] : ['Conectividad de Alta Velocidad', 'Gestión Centralizada', 'Seguridad Mejorada', 'Eficiencia de Costos'],
      slug: 'branch-office'
    },
    {
      id: 'managed-internet',
      title: language === 'en' ? 'Managed Enterprise Internet' : 'Internet Empresarial Gestionado',
      description: language === 'en' ? 'End-to-end internet infrastructure management with proactive monitoring and enterprise-grade security.' : 'Gestión integral de infraestructura de internet con monitoreo proactivo y seguridad de grado empresarial.',
      icon: Wifi,
      features: language === 'en' ? ['24/7 Monitoring', 'Enterprise Security', 'Scalable Solutions', 'Expert Support'] : ['Monitoreo 24/7', 'Seguridad Empresarial', 'Soluciones Escalables', 'Soporte Experto'],
      slug: 'managed-internet'
    },
    {
      id: 'international-connectivity',
      title: language === 'en' ? 'International Connectivity' : 'Conectividad Internacional',
      description: language === 'en' ? 'MPLS, SD-WAN and VPL solutions for global business connectivity with optimized performance and security.' : 'Soluciones MPLS, SD-WAN y VPL para conectividad empresarial global con rendimiento optimizado y seguridad.',
      icon: Globe,
      features: language === 'en' ? ['MPLS Networks', 'SD-WAN Solutions', 'VPL (Layer 2 VPN)', 'Global Reach'] : ['Redes MPLS', 'Soluciones SD-WAN', 'VPL (VPN de Capa 2)', 'Alcance Global'],
      slug: 'international-connectivity'
    },
    {
      id: 'domestic-services',
      title: language === 'en' ? 'Domestic Internet Services' : 'Servicios de Internet Doméstico',
      description: language === 'en' ? 'High-speed home internet with reliable connectivity, affordable plans, and exceptional customer support.' : 'Internet doméstico de alta velocidad con conectividad confiable, planes asequibles y soporte al cliente excepcional.',
      icon: Home,
      features: language === 'en' ? ['Blazing-Fast Speeds', 'Affordable Plans', 'Reliable Connectivity', '24/7 Support'] : ['Velocidades Ultra Rápidas', 'Planes Asequibles', 'Conectividad Confiable', 'Soporte 24/7'],
      slug: '/domestic-services'
    }
  ];

  // Use dynamic content if available, otherwise fallback
  const networkSolutions = dynamicNetworkSolutions.length > 0 ? dynamicNetworkSolutions : fallbackNetworkSolutions;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <DynamicContent
        identifier="network-solutions-hero"
        className="bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 text-white"
        fallback={
          <section className="bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 text-white">
            <div className="container mx-auto px-4 py-20">
              <div className="max-w-4xl mx-auto text-center">
                <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-200">
                  {language === 'en' ? 'Enterprise Network Solutions' : 'Soluciones de Red Empresariales'}
                </Badge>
                <h1 className="text-5xl font-bold mb-6">
                  {language === 'en' ? 'Connect Your Business to Success' : 'Conecta Tu Negocio al Éxito'}
                </h1>
                <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                  {language === 'en'
                    ? 'OfficeTech Guinea delivers cutting-edge network infrastructure solutions that keep your business connected, secure, and competitive in today\'s digital landscape.'
                    : 'OfficeTech Guinea ofrece soluciones de infraestructura de red de vanguardia que mantienen tu negocio conectado, seguro y competitivo en el panorama digital actual.'
                  }
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link to="/contact">
                    <Button size="lg" className="bg-white text-blue-900 hover:bg-gray-100">
                      <Phone className="w-5 h-5 mr-2" />
                      {language === 'en' ? 'Get Free Consultation' : 'Consulta Gratuita'}
                    </Button>
                  </Link>
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-900">
                    <ArrowRight className="w-5 h-5 mr-2" />
                    {language === 'en' ? 'View Solutions' : 'Ver Soluciones'}
                  </Button>
                </div>
              </div>
            </div>
          </section>
        }
      />

      {/* Key Statistics */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
              <p className="text-gray-600">{language === 'en' ? 'Businesses Connected' : 'Empresas Conectadas'}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">99.9%</div>
              <p className="text-gray-600">{language === 'en' ? 'Network Uptime' : 'Tiempo de Actividad'}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">24/7</div>
              <p className="text-gray-600">{language === 'en' ? 'Technical Support' : 'Soporte Técnico'}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-600 mb-2">15+</div>
              <p className="text-gray-600">{language === 'en' ? 'Years Experience' : 'Años de Experiencia'}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Solutions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {language === 'en' ? 'Our Network Solutions' : 'Nuestras Soluciones de Red'}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {language === 'en'
                ? 'From national connectivity to enterprise internet services, we provide comprehensive network solutions tailored to your business needs.'
                : 'Desde conectividad nacional hasta servicios de internet empresarial, proporcionamos soluciones de red integrales adaptadas a las necesidades de tu negocio.'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {networkSolutions.map((solution) => {
              // Handle both dynamic content and fallback data
              const IconComponent = solution.icon || Network;
              const title = solution.data?.title || solution.title;
              const description = solution.data?.description || solution.description;
              const features = solution.data?.features || solution.features || [];
              const slug = solution.data?.slug || solution.slug;
              const linkPath = slug.startsWith('/') ? slug : `/network-solutions/${slug}`;

              return (
                <Link key={solution.id || solution._id} to={linkPath}>
                  <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg cursor-pointer h-full">
                    <CardHeader className="text-center pb-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                        <IconComponent className="w-8 h-8 text-blue-600" />
                      </div>
                      <CardTitle className="text-2xl text-gray-900">{title}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-gray-600 text-center">
                        {description}
                      </p>

                      <div className="space-y-3">
                        {features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </div>
                        ))}
                      </div>

                      <div className="pt-4">
                        <Button className="w-full group-hover:bg-blue-700">
                          {language === 'en' ? 'Learn More' : 'Saber Más'}
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose OfficeTech Guinea?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We combine cutting-edge technology with local expertise to deliver 
              network solutions that drive business growth.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Enterprise Security</h3>
              <p className="text-gray-600">
                Advanced security protocols and monitoring to protect your business data and communications.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">High Performance</h3>
              <p className="text-gray-600">
                Lightning-fast connections with guaranteed bandwidth and minimal latency for optimal productivity.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Expert Support</h3>
              <p className="text-gray-600">
                Dedicated technical support team available 24/7 to ensure your network runs smoothly.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Scalable Solutions</h3>
              <p className="text-gray-600">
                Flexible network infrastructure that grows with your business needs and requirements.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Client Testimonial */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-2xl text-gray-700 italic mb-8">
              "OfficeTech Guinea transformed our network infrastructure. We now have reliable 
              connectivity across all our locations with exceptional support. Their expertise 
              in enterprise networking is unmatched in Guinea."
            </blockquote>
            <div className="flex items-center justify-center space-x-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-left">
                <p className="font-semibold text-gray-900">Mamadou Diallo</p>
                <p className="text-gray-600">IT Director, Guinea Mining Corp</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Get Your Free Network Consultation
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Ready to transform your network infrastructure? Contact our experts for a
              personalized consultation and custom solution design.
            </p>
          </div>

          <NetworkSolutionsContact />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            {language === 'en' ? 'Ready to Transform Your Network?' : '¿Listo para Transformar tu Red?'}
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            {language === 'en'
              ? 'Get a free consultation and discover how our network solutions can accelerate your business growth.'
              : 'Obtén una consulta gratuita y descubre cómo nuestras soluciones de red pueden acelerar el crecimiento de tu negocio.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <a href="tel:+240555444222">
                <Phone className="w-5 h-5 mr-2" />
                {language === 'en' ? 'Call +240 555 444 222' : 'Llamar +240 555 444 222'}
              </a>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link to="/contact">
                <Mail className="w-5 h-5 mr-2" />
                {language === 'en' ? 'Request Quote' : 'Solicitar Cotización'}
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export const NetworkSolutions = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <Layout>
        <NetworkSolutionsContent />
      </Layout>
    </ContentProvider>
  );
};
