import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { DynamicContent } from '@/components/content/DynamicContent';
import { GraduationCap, Users, Clock, Award, BookOpen, Monitor, Target, TrendingUp, ArrowRight, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { useTranslation, useLanguage } from '@/contexts/I18nContext';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

const TrainingProgramsContent = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();

  // Get all training program cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training program cards and sort by order
  const dynamicTrainingPrograms = allContent?.filter(item =>
    item.contentType?.name === "training_program" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback training programs with bilingual support
  const fallbackPrograms = [
    {
      id: 'corporate-pc-skills',
      title: 'Corporate PC Skills Development',
      duration: '2-4 weeks',
      level: 'All Levels',
      participants: '15-20',
      description: 'Comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.',
      topics: [
        'Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)',
        'Windows Operating Systems navigation and troubleshooting',
        'Cybersecurity Awareness and best practices',
        'Network Fundamentals and basic troubleshooting',
        'Custom Training tailored to business needs'
      ],
      icon: Monitor,
      price: 'Contact for pricing',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: 'Leadership Development Program',
      duration: '3-5 days',
      level: 'Management',
      participants: '10-15',
      description: 'Essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.',
      topics: [
        'Self-Awareness and Emotional Intelligence',
        'Effective Communication and Active Listening',
        'Building Trust and Influence',
        'Decision-Making and Problem-Solving',
        'Team Collaboration and Empowerment',
        'Adaptability and Change Management',
        'Visionary Leadership'
      ],
      icon: Target,
      price: 'Contact for pricing',
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: 'Time Management Program',
      duration: '2-3 days',
      level: 'All Levels',
      participants: '15-25',
      description: 'Help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress.',
      topics: [
        'Understanding Time Management principles',
        'Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)',
        'Planning and Scheduling techniques',
        'Overcoming Procrastination',
        'Managing Distractions and focus techniques',
        'Delegation and Collaboration',
        'Stress Management and Work-Life Balance'
      ],
      icon: Clock,
      price: 'Contact for pricing',
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: 'Communication Skills Program',
      duration: '2-4 days',
      level: 'All Levels',
      participants: '12-20',
      description: 'Master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes.',
      topics: [
        'Foundations of Effective Communication',
        'Active Listening techniques',
        'Verbal and Non-Verbal Communication',
        'Written Communication skills',
        'Emotional Intelligence in Communication',
        'Conflict Resolution and Difficult Conversations',
        'Public Speaking and Presentation Skills'
      ],
      icon: Users,
      price: 'Contact for pricing',
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: 'Professional Oil & Gas Training',
      duration: 'Varies',
      level: 'Professional',
      participants: '8-15',
      description: 'Certified, practical, and industry-relevant training to meet global energy sector standards with internationally recognized certifications.',
      topics: [
        'Offshore/Onshore Safety (OPITO, IWCF, IADC)',
        'Process Operations & Maintenance',
        'Pipeline & Refinery Operations',
        'HSE (Health, Safety & Environment)',
        'Technical & Soft Skills Development'
      ],
      icon: Award,
      price: 'Contact for pricing',
      slug: 'oil-gas-training'
    }
  ];

  const oldPrograms = [
    {
      title: 'Corporate PC Skills',
      duration: '2 weeks',
      level: 'Beginner',
      participants: '15-20',
      description: 'Essential computer skills for the modern workplace including file management, internet usage, and basic troubleshooting.',
      topics: [
        'Computer basics and navigation',
        'File and folder management',
        'Internet browsing and email',
        'Basic troubleshooting',
        'Digital security awareness'
      ],
      icon: Monitor,
      price: '$150'
    },
    {
      title: 'Microsoft Office Suite',
      duration: '3 weeks',
      level: 'Intermediate',
      participants: '12-15',
      description: 'Comprehensive training on Word, Excel, PowerPoint, and Outlook for professional productivity.',
      topics: [
        'Microsoft Word advanced features',
        'Excel formulas and data analysis',
        'PowerPoint presentation design',
        'Outlook email management',
        'Integration between Office apps'
      ],
      icon: BookOpen,
      price: '$200'
    },
    {
      title: 'Windows Administration',
      duration: '4 weeks',
      level: 'Advanced',
      participants: '8-12',
      description: 'Advanced Windows system administration for IT professionals and power users.',
      topics: [
        'Windows Server management',
        'User and group administration',
        'Network configuration',
        'Security policies',
        'System monitoring and maintenance'
      ],
      icon: Target,
      price: '$300'
    },
    {
      title: 'Leadership Development',
      duration: '2 weeks',
      level: 'All Levels',
      participants: '10-15',
      description: 'Essential leadership skills for managers and team leaders in the digital age.',
      topics: [
        'Leadership fundamentals',
        'Team management',
        'Communication skills',
        'Conflict resolution',
        'Digital leadership tools'
      ],
      icon: Users,
      price: '$180'
    },
    {
      title: 'Time Management',
      duration: '1 week',
      level: 'All Levels',
      participants: '15-25',
      description: 'Effective time management strategies and digital tools for increased productivity.',
      topics: [
        'Time management principles',
        'Priority setting techniques',
        'Digital productivity tools',
        'Work-life balance',
        'Stress management'
      ],
      icon: Clock,
      price: '$100'
    }
  ];

  const benefits = [
    {
      icon: Award,
      title: 'Certified Training',
      description: 'Receive official certificates upon successful completion of programs.'
    },
    {
      icon: Users,
      title: 'Expert Instructors',
      description: 'Learn from experienced professionals with industry expertise.'
    },
    {
      icon: TrendingUp,
      title: 'Career Growth',
      description: 'Enhance your skills and advance your career opportunities.'
    },
    {
      icon: Monitor,
      title: 'Hands-on Learning',
      description: 'Practical exercises and real-world scenarios for better understanding.'
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  // Use dynamic content if available, otherwise fallback
  const programs = dynamicTrainingPrograms.length > 0 ? dynamicTrainingPrograms : fallbackPrograms;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      {/* Hero Section */}
      <DynamicContent
        identifier="training-programs-hero"
        className="relative py-20 px-4 sm:px-6 lg:px-8"
        fallback={
          <section className="relative py-20 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <span className="text-gradient">{language === 'en' ? 'Training Programs' : 'Programas de Entrenamiento'}</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {language === 'en'
                  ? 'Professional development and technical training programs designed to enhance your skills and advance your career in the digital age.'
                  : 'Programas de desarrollo profesional y entrenamiento técnico diseñados para mejorar tus habilidades y avanzar en tu carrera en la era digital.'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <Link to="/contact">
                  <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4">
                    {language === 'en' ? 'Enroll Now' : 'Inscribirse Ahora'}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="px-8 py-4">
                  {language === 'en' ? 'View Programs' : 'Ver Programas'}
                </Button>
              </div>
            </div>
          </section>
        }
      />

        {/* Benefits Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {language === 'en' ? 'Why Choose Our Training?' : '¿Por Qué Elegir Nuestro Entrenamiento?'}
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {language === 'en'
                  ? 'Our comprehensive training programs are designed to provide practical skills and knowledge that you can immediately apply in your work.'
                  : 'Nuestros programas de entrenamiento integrales están diseñados para proporcionar habilidades prácticas y conocimientos que puedes aplicar inmediatamente en tu trabajo.'
                }
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-6 h-6 text-purple-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Programs Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {language === 'en' ? 'Available Programs' : 'Programas Disponibles'}
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {language === 'en'
                  ? 'Choose from our range of professional training programs tailored to different skill levels and career goals.'
                  : 'Elige de nuestra gama de programas de entrenamiento profesional adaptados a diferentes niveles de habilidad y objetivos de carrera.'
                }
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {programs.map((program, index) => {
                // Handle both dynamic content and fallback data
                const Icon = program.icon || GraduationCap;
                const title = program.data?.title || program.title;
                const description = program.data?.description || program.description;
                const duration = program.data?.duration || program.duration;
                const level = program.data?.level || program.level;
                const participants = program.data?.participants || program.participants;
                const topics = program.data?.modules || program.data?.topics || program.topics || [];
                const price = program.data?.price || program.price;
                const slug = program.data?.slug || program.slug;

                return (
                  <Link key={program.id || program._id} to={`/training/${slug}`}>
                    <Card className="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-full group">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                              <Icon className="w-5 h-5 text-purple-600" />
                            </div>
                            <div>
                              <h3 className="text-xl font-bold text-gray-900">
                                {title}
                              </h3>
                              <Badge className={getLevelColor(level)}>
                                {level}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-purple-600">
                              {price}
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-600 mb-4 line-clamp-2">
                          {description}
                        </p>

                        <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                          <div className="text-center">
                            <Clock className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                            <div className="font-medium text-gray-900">{duration}</div>
                            <div className="text-gray-500">{language === 'en' ? 'Duration' : 'Duración'}</div>
                          </div>
                          <div className="text-center">
                            <Users className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                            <div className="font-medium text-gray-900">{participants}</div>
                            <div className="text-gray-500">{language === 'en' ? 'Participants' : 'Participantes'}</div>
                          </div>
                          <div className="text-center">
                            <GraduationCap className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                            <div className="font-medium text-gray-900">{language === 'en' ? 'Certificate' : 'Certificado'}</div>
                            <div className="text-gray-500">{language === 'en' ? 'Included' : 'Incluido'}</div>
                          </div>
                        </div>

                        <div className="mb-6">
                          <h4 className="font-semibold text-gray-900 mb-2">{language === 'en' ? 'Training Modules:' : 'Módulos de Entrenamiento:'}</h4>
                          <ul className="space-y-1">
                            {topics.slice(0, 3).map((topic, topicIndex) => (
                              <li key={topicIndex} className="text-sm text-gray-600 flex items-center">
                                <CheckCircle className="w-3 h-3 text-green-600 mr-2 flex-shrink-0" />
                                {topic}
                              </li>
                            ))}
                            {topics.length > 3 && (
                              <li className="text-sm text-gray-500 italic">
                                +{topics.length - 3} {language === 'en' ? 'more modules...' : 'más módulos...'}
                              </li>
                            )}
                          </ul>
                        </div>

                        <div className="flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors">
                          <span>Learn More</span>
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
              <GraduationCap className="w-16 h-16 mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {language === 'en' ? 'Ready to Advance Your Skills?' : '¿Listo para Avanzar tus Habilidades?'}
              </h2>
              <p className="text-xl mb-6 opacity-90">
                {language === 'en'
                  ? 'Join our training programs and take your career to the next level with practical, industry-relevant skills.'
                  : 'Únete a nuestros programas de entrenamiento y lleva tu carrera al siguiente nivel con habilidades prácticas y relevantes para la industria.'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" variant="secondary">
                  <Link to="/contact">
                    {language === 'en' ? 'View Schedule' : 'Ver Horario'}
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-purple-600">
                  <Link to="/contact">
                    {language === 'en' ? 'Contact Us' : 'Contáctanos'}
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
  );
};

const TrainingPrograms = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <Layout>
        <TrainingProgramsContent />
      </Layout>
    </ContentProvider>
  );
};

export default TrainingPrograms;
