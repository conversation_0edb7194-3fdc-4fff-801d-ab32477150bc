import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ChevronDown } from 'lucide-react';
import * as Icons from "lucide-react";
import { useLanguage, useTranslation } from '@/contexts/I18nContext';
import { useAnalyticsContext } from '@/components/analytics/AnalyticsProvider';

interface ServicesDropdownProps {
  isActive: boolean;
  className?: string;
}

export const ServicesDropdown = ({ isActive, className = "" }: ServicesDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { language } = useLanguage();
  const { t } = useTranslation();
  const { trackContent } = useAnalyticsContext();

  // Get hierarchical service structure
  const hierarchicalServices = useQuery(api.reorganizeServices.getHierarchicalServices, {
    language: language,
    includeSubServices: true
  }) || [];

  const getIconComponent = (iconName: string) => {
    if (!iconName) return Icons.Briefcase;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent || Icons.Briefcase;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => {
          setIsOpen(!isOpen);
          trackContent('services-dropdown', 'navigation', isOpen ? 'close' : 'open');
        }}
        className={`font-medium transition-colors duration-200 flex items-center ${className} ${
          isActive
            ? 'text-blue-600 border-b-2 border-blue-600'
            : 'text-gray-700 hover:text-blue-600'
        }`}
      >
        {t('nav.services')}
        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* All Services Link */}
          <Link
            to="/services"
            className="block px-4 py-3 text-gray-900 hover:bg-gray-50 border-b border-gray-100"
            onClick={() => {
              setIsOpen(false);
              trackContent('services-dropdown-all', 'navigation', 'click', { destination: '/services' });
            }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Icons.Grid3X3 className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">{language === 'en' ? 'All Services' : 'Todos los Servicios'}</div>
                <div className="text-sm text-gray-500">{language === 'en' ? 'View our complete service portfolio' : 'Ver nuestro portafolio completo de servicios'}</div>
              </div>
            </div>
          </Link>

          {/* Hierarchical Service Links */}
          {hierarchicalServices.map((mainService) => {
            const IconComponent = getIconComponent(mainService.data.icon);

            // Extract slug from identifier and determine the link
            const getServiceLink = (identifier: string, slug?: string) => {
              if (slug) {
                return `/services/${slug}`;
              }
              if (identifier.startsWith('service-')) {
                const serviceSlug = identifier.replace('service-', '');
                // Special cases for services that have different routing
                switch (serviceSlug) {
                  case 'training':
                    return '/training';
                  case 'isp':
                    return '/services/internet-services';
                  case 'procurement':
                    return '/services/office-supplies-equipment';
                  case 'business-solutions':
                    return '/services/business-solutions';
                  default:
                    return `/services/${serviceSlug}`;
                }
              }
              return '/contact';
            };

            const mainServiceLink = getServiceLink(mainService.identifier, mainService.data.slug);

            return (
              <div key={mainService._id}>
                {/* Main Service */}
                <Link
                  to={mainServiceLink}
                  className="block px-4 py-3 text-gray-900 hover:bg-gray-50 transition-colors"
                  onClick={() => {
                    setIsOpen(false);
                    trackContent(`services-dropdown-${mainService.identifier}`, 'navigation', 'click', {
                      destination: mainServiceLink,
                      serviceName: mainService.data.title,
                      serviceId: mainService._id
                    });
                  }}
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <IconComponent className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">{mainService.data.title}</div>
                      <div
                        className="text-sm text-gray-500 line-clamp-1"
                        dangerouslySetInnerHTML={{
                          __html: mainService.data.description.replace(/<[^>]*>/g, '').substring(0, 60) + '...'
                        }}
                      />
                    </div>
                  </div>
                </Link>

                {/* Sub-services */}
                {mainService.children && mainService.children.length > 0 && (
                  <div className="ml-4 border-l border-gray-200">
                    {mainService.children.map((subService) => {
                      const SubIconComponent = getIconComponent(subService.data.icon);
                      const subServiceLink = getServiceLink(subService.identifier, subService.data.slug);

                      return (
                        <Link
                          key={subService._id}
                          to={subServiceLink}
                          className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => {
                            setIsOpen(false);
                            trackContent(`services-dropdown-${subService.identifier}`, 'navigation', 'click', {
                              destination: subServiceLink,
                              serviceName: subService.data.title,
                              serviceId: subService._id,
                              parentService: mainService.data.title
                            });
                          }}
                        >
                          <div className="flex items-center">
                            <div className="w-6 h-6 bg-gray-100 rounded-md flex items-center justify-center mr-3">
                              <SubIconComponent className="w-3 h-3 text-gray-600" />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{subService.data.title}</div>
                              <div
                                className="text-xs text-gray-500 line-clamp-1"
                                dangerouslySetInnerHTML={{
                                  __html: subService.data.description.replace(/<[^>]*>/g, '').substring(0, 50) + '...'
                                }}
                              />
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}

          {hierarchicalServices.length === 0 && (
            <div className="px-4 py-3 text-gray-500 text-sm">
              No services available
            </div>
          )}
        </div>
      )}
    </div>
  );
};
