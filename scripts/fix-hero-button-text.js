#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to fix hero button text
import { ConvexHttpClient } from "convex/browser";

async function fixHeroButtonText() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      console.log("Make sure Convex is running with 'npx convex dev'");
      process.exit(1);
    }

    console.log("🚀 Fixing hero button text...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Fix the button text
    console.log("🔧 Updating hero button text...");
    const result = await client.mutation("setup:fixHeroButtonText", {});

    if (result.success) {
      console.log("✅ Hero button text updated successfully!");
      console.log("📊 Results:", result.results);
      console.log("🔗 Button text updated to:", result.buttonText);
    } else {
      console.error("❌ Failed to update hero button text:", result.message);
      process.exit(1);
    }

  } catch (error) {
    console.error("❌ Failed to fix hero button text:", error);
    process.exit(1);
  }
}

// Run the script
fixHeroButtonText();
