#!/usr/bin/env tsx

import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient(process.env.VITE_CONVEX_URL!);

async function updateServices() {
  try {
    console.log("🚀 Starting service update to 4-unit structure...");
    
    const result = await client.mutation("updateServices:updateServicesTo4Units", {});
    
    console.log("✅ Services updated successfully!");
    console.log(`📊 Created ${result.servicesCreated} service units`);
    console.log("📋 Service units created:");
    console.log("  1. Training - Professional IT training and certification programs");
    console.log("  2. ISP - Internet Service Provider solutions");
    console.log("  3. Office Supplies & Equipment - Complete procurement solutions");
    console.log("  4. Business Solutions - Comprehensive technology solutions");
    
    console.log("\n🌐 All services are available in both English and Spanish");
    console.log("🔗 Each service has detailed descriptions and individual detail pages");
    
  } catch (error) {
    console.error("❌ Error updating services:", error);
    process.exit(1);
  }
}

updateServices();
