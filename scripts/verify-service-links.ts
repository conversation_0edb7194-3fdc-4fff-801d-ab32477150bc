#!/usr/bin/env tsx

import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient(process.env.VITE_CONVEX_URL!);

async function verifyServiceLinks() {
  try {
    console.log("🔍 Verifying service detail page links...");
    
    // Get all English service cards
    const allContent = await client.query("content:getAllContent", {
      language: "en",
      status: "published"
    });
    
    const serviceCards = allContent.filter((item: any) => 
      item.contentType?.name === "service_card"
    ).sort((a: any, b: any) => (a.data.order || 0) - (b.data.order || 0));
    
    console.log(`\n📋 Found ${serviceCards.length} service cards:`);
    
    serviceCards.forEach((card: any, index: number) => {
      const slug = card.data.slug;
      const title = card.data.title;
      const identifier = card.identifier;
      const url = `http://localhost:8081/services/${slug}`;
      
      console.log(`${index + 1}. ${title}`);
      console.log(`   Identifier: ${identifier}`);
      console.log(`   Slug: ${slug}`);
      console.log(`   URL: ${url}`);
      console.log(`   ✅ Link should work\n`);
    });
    
    console.log("🎉 All service detail pages should be accessible!");
    console.log("\n📝 Service URLs to test:");
    serviceCards.forEach((card: any) => {
      console.log(`   - http://localhost:8081/services/${card.data.slug}`);
    });
    
  } catch (error) {
    console.error("❌ Error verifying service links:", error);
    process.exit(1);
  }
}

verifyServiceLinks();
